﻿using Business.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UserLicensesController : ControllerBase
    {
        private readonly IUserLicenseService _userLicenseService;

        public UserLicensesController(IUserLicenseService userLicenseService)
        {
            _userLicenseService = userLicenseService;
        }

        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _userLicenseService.GetAll();
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getallpaginated")]
        public IActionResult GetAllPaginated(
            int page = 1,
            int pageSize = 20,
            string searchTerm = "",
            string sortBy = "newest",
            string companyName = "",
            int? remainingDaysMin = null,
            int? remainingDaysMax = null)
        {
            var result = _userLicenseService.GetAllPaginated(page, pageSize, searchTerm, sortBy, companyName, remainingDaysMin, remainingDaysMax);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getexpiredandpassive")]
        public IActionResult GetExpiredAndPassive(
            int page = 1,
            int pageSize = 20,
            string searchTerm = "")
        {
            var result = _userLicenseService.GetExpiredAndPassive(page, pageSize, searchTerm);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getbyid")]
        public IActionResult GetById(int id)
        {
            var result = _userLicenseService.GetById(id);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getactivebyuserid")]
        public IActionResult GetActiveByUserId(int userId)
        {
            var result = _userLicenseService.GetActiveByUserId(userId);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getmyactivelicenses")]
        public IActionResult GetMyActiveLicenses()
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var result = _userLicenseService.GetMyActiveLicenses(userId);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getuserroles")]
        public IActionResult GetUserRoles(int userId)
        {
            var result = _userLicenseService.GetUserRoles(userId);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("add")]
        public IActionResult Add(UserLicense userLicense)
        {
            var result = _userLicenseService.Add(userLicense);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("update")]
        public IActionResult Update(UserLicense userLicense)
        {
            var result = _userLicenseService.Update(userLicense);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpDelete("delete")]
        public IActionResult Delete(int id)
        {
            var result = _userLicenseService.Delete(id);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("purchase")]
        public IActionResult Purchase(LicensePurchaseDto licensePurchaseDto)
        {
            var result = _userLicenseService.Purchase(licensePurchaseDto);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("extend")]
        public IActionResult ExtendLicense(LicenseExtensionDto licenseExtensionDto)
        {
            var result = _userLicenseService.ExtendLicense(licenseExtensionDto);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("extendbypackage")]
        public IActionResult ExtendLicenseByPackage(LicenseExtensionByPackageDto licenseExtensionByPackageDto)
        {
            var result = _userLicenseService.ExtendLicenseByPackage(licenseExtensionByPackageDto);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("revoke")]
        public IActionResult RevokeLicense(int userLicenseId)
        {
            var result = _userLicenseService.RevokeLicense(userLicenseId);
            return result.Success ? Ok(result) : BadRequest(result);
        }
    }
}